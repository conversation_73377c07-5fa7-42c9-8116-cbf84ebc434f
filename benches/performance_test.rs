use criterion::{black_box, criterion_group, criterion_main, Criterion};
use sdk3_generator::{Path<PERSON><PERSON>s, DefaultGenerator, InterfaceInfoVO, ParamPO, ResultCriteriaPO};
use sdk3_generator::generator::{FastStringWriter, StringBufferUtils};
use std::collections::HashMap;

fn benchmark_string_building(c: &mut Criterion) {
    c.bench_function("fast_string_writer", |b| {
        b.iter(|| {
            let mut writer = FastStringWriter::new();
            for i in 0..1000 {
                writer.append(&format!("Line {}\n", i));
            }
            black_box(writer.into_string())
        })
    });

    c.bench_function("string_buffer_utils", |b| {
        b.iter(|| {
            let mut buffer = StringBufferUtils::new();
            for i in 0..1000 {
                buffer.append_one(&format!("Line {}\n", i));
            }
            black_box(buffer.into_string())
        })
    });
}

fn benchmark_path_operations(c: &mut Criterion) {
    c.bench_function("path_utils_folder_path", |b| {
        b.iter(|| {
            black_box(PathUtils::get_folder_model_path(
                "test.app",
                false,
                "tenant",
                "dev",
                "/tmp/base"
            ))
        })
    });

    c.bench_function("path_utils_class_name", |b| {
        b.iter(|| {
            black_box(PathUtils::class_name("test_class_name_with_underscores"))
        })
    });
}

fn benchmark_chinese_processing(c: &mut Criterion) {
    c.bench_function("chinese_to_pinyin", |b| {
        b.iter(|| {
            black_box(DefaultGenerator::class_name("测试类名"))
        })
    });

    c.bench_function("mixed_text_processing", |b| {
        b.iter(|| {
            black_box(DefaultGenerator::class_name("Test测试Class类名123"))
        })
    });
}

fn benchmark_hashmap_operations(c: &mut Criterion) {
    // Create test data
    let mut interfaces_map: HashMap<String, Vec<InterfaceInfoVO>> = HashMap::new();
    let mut params_map: HashMap<String, Vec<ParamPO>> = HashMap::new();
    let criteria_map: HashMap<String, ResultCriteriaPO> = HashMap::new();

    // Add test data
    for i in 0..100 {
        let app_id = format!("app_{}", i);
        let interface_id = format!("interface_{}", i);

        // Create mock interface
        let interface = InterfaceInfoVO {
            id: interface_id.clone(),
            app_id: app_id.clone(),
            name: format!("Interface {}", i),
            alias: format!("alias_{}", i),
            uri: format!("/api/v1/test_{}", i),
            desc: Some(format!("Test interface {}", i)),
            request_method: "GET".to_string(),
            service_name: Some(format!("service_{}", i)),
            request: Vec::new(),
            response: Vec::new(),
        };

        interfaces_map.insert(app_id, vec![interface]);

        // Create mock params
        let params = vec![
            ParamPO {
                id: format!("param_req_{}", i),
                interface_id: interface_id.clone(),
                position: "requestBody".to_string(),
                name: format!("req_param_{}", i),
                data_type: Default::default(),
                required: Some(true),
                desc: Some("Request parameter".to_string()),
            },
            ParamPO {
                id: format!("param_resp_{}", i),
                interface_id: interface_id.clone(),
                position: "responseBody".to_string(),
                name: format!("resp_param_{}", i),
                data_type: Default::default(),
                required: Some(false),
                desc: Some("Response parameter".to_string()),
            },
        ];

        params_map.insert(interface_id, params);
    }

    c.bench_function("hashmap_multiple_gets_with_clone", |b| {
        b.iter(|| {
            // Simulate the old approach with multiple gets and clones
            for i in 0..10 {
                let app_id = format!("app_{}", i);
                let _interfaces = interfaces_map.get(&app_id).cloned().unwrap_or_default();

                for interface in &_interfaces {
                    let _params = params_map.get(&interface.id).cloned().unwrap_or_default();
                    let _criteria = criteria_map.get(&interface.id).cloned();
                }
            }
            black_box(())
        })
    });

    c.bench_function("hashmap_optimized_single_get", |b| {
        b.iter(|| {
            // Simulate the optimized approach with single get and minimal cloning
            for i in 0..10 {
                let app_id = format!("app_{}", i);
                if let Some(interfaces) = interfaces_map.get(&app_id) {
                    for interface in interfaces {
                        if let Some(_params) = params_map.get(&interface.id) {
                            // Only access, don't clone unless necessary
                        }
                        if let Some(_criteria) = criteria_map.get(&interface.id) {
                            // Only access, don't clone unless necessary
                        }
                    }
                }
            }
            black_box(())
        })
    });
}

criterion_group!(
    benches,
    benchmark_string_building,
    benchmark_path_operations,
    benchmark_chinese_processing,
    benchmark_hashmap_operations
);
criterion_main!(benches);
