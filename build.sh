#!/bin/bash

# SDK3 Generator Build Script

set -e

echo "Building SDK3 Generator..."

# Check if Rust is installed
if ! command -v cargo &> /dev/null; then
    echo "Error: Rust/Cargo is not installed. Please install from https://rustup.rs/"
    exit 1
fi

# Clean previous builds
echo "Cleaning previous builds..."
cargo clean

# Build in release mode
echo "Building in release mode..."
cargo build --release

# Run tests
echo "Running tests..."
cargo test

# Check if build was successful
if [ -f "target/release/sdk3" ]; then
    echo "✅ Build successful!"
    echo "Executable location: target/release/sdk3"
    echo ""
    echo "Usage example:"
    echo "./target/release/sdk3 \\"
    echo "  --connection-string 'mongodb://localhost:27017' \\"
    echo "  --database 'api_management' \\"
    echo "  --tenant-id 'your_tenant_id' \\"
    echo "  --env 'dev'"
else
    echo "❌ Build failed!"
    exit 1
fi

echo ""
echo "To install globally, run:"
echo "cargo install --path ."
