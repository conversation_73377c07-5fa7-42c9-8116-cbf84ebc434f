# SDK3 Generator Configuration Example
# Copy this file to config.toml and modify as needed

[database]
connection_string = "mongodb://localhost:27017"
database_name = "api_management"

[generation]
# Whether this is an open tenant
is_open = false

# Source template path (where base templates are stored)
source_path = "/tmp/sdk_templates"

# Target output path (where generated files will be placed)
target_path = "/tmp/sdk_generation"

# Environment (dev/prod)
environment = "dev"

[logging]
# Log level: debug, info, warn, error
level = "info"

[templates]
# Custom template paths (optional)
model_template = "templates/model.hbs"
api_template = "templates/api.hbs"

[output]
# Java package prefix
package_prefix = "com.client.sdk"

# Whether to clean target directory before generation
clean_target = true

# Whether to generate documentation
generate_docs = true
