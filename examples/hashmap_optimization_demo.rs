use std::collections::HashMap;
use std::time::Instant;

/// Demo showing the performance difference between multiple HashMap gets with clone
/// vs optimized single get with minimal cloning

#[derive(<PERSON><PERSON>, Debug)]
struct MockData {
    id: String,
    name: String,
    data: Vec<String>,
}

impl MockData {
    fn new(id: &str, name: &str, size: usize) -> Self {
        Self {
            id: id.to_string(),
            name: name.to_string(),
            data: (0..size).map(|i| format!("data_{}", i)).collect(),
        }
    }
}

/// Original approach - multiple gets with immediate cloning
fn original_approach(data_map: &HashMap<String, Vec<MockData>>, keys: &[String]) -> Vec<MockData> {
    let mut result = Vec::new();
    
    for key in keys {
        // Multiple gets with immediate cloning - inefficient
        let data = data_map.get(key).cloned().unwrap_or_default();
        for item in data {
            // Process each item (simulate some work)
            let processed = MockData {
                id: format!("processed_{}", item.id),
                name: item.name.clone(),
                data: item.data.clone(),
            };
            result.push(processed);
        }
    }
    
    result
}

/// Optimized approach - single get with minimal cloning
fn optimized_approach(data_map: &HashMap<String, Vec<MockData>>, keys: &[String]) -> Vec<MockData> {
    let mut result = Vec::new();
    
    for key in keys {
        // Single get, work with reference first
        if let Some(data_ref) = data_map.get(key) {
            // Pre-allocate result space
            result.reserve(data_ref.len());
            
            for item in data_ref {
                // Only clone when we actually need ownership
                let processed = MockData {
                    id: format!("processed_{}", item.id),
                    name: item.name.clone(),
                    data: item.data.clone(),
                };
                result.push(processed);
            }
        }
    }
    
    result
}

/// Even more optimized approach - early return and better memory management
fn super_optimized_approach(data_map: &HashMap<String, Vec<MockData>>, keys: &[String]) -> Vec<MockData> {
    // Estimate total capacity to reduce reallocations
    let estimated_capacity: usize = keys.iter()
        .filter_map(|key| data_map.get(key))
        .map(|data| data.len())
        .sum();
    
    let mut result = Vec::with_capacity(estimated_capacity);
    
    for key in keys {
        // Early return if no data
        let data_ref = match data_map.get(key) {
            Some(data) => data,
            None => continue, // Skip missing keys
        };
        
        // Process with minimal cloning
        for item in data_ref {
            let processed = MockData {
                id: format!("processed_{}", item.id),
                name: item.name.clone(),
                data: item.data.clone(),
            };
            result.push(processed);
        }
    }
    
    result
}

fn main() {
    println!("HashMap Optimization Demo");
    println!("========================");
    
    // Create test data
    let mut data_map: HashMap<String, Vec<MockData>> = HashMap::new();
    
    // Add test data
    for i in 0..100 {
        let key = format!("key_{}", i);
        let data = vec![
            MockData::new(&format!("item_{}_{}", i, 0), &format!("name_{}_{}", i, 0), 50),
            MockData::new(&format!("item_{}_{}", i, 1), &format!("name_{}_{}", i, 1), 50),
            MockData::new(&format!("item_{}_{}", i, 2), &format!("name_{}_{}", i, 2), 50),
        ];
        data_map.insert(key, data);
    }
    
    // Test keys
    let test_keys: Vec<String> = (0..50).map(|i| format!("key_{}", i)).collect();
    
    println!("Testing with {} keys, each containing 3 items with 50 data elements", test_keys.len());
    println!();
    
    // Benchmark original approach
    let start = Instant::now();
    let result1 = original_approach(&data_map, &test_keys);
    let duration1 = start.elapsed();
    println!("Original approach:");
    println!("  Time: {:?}", duration1);
    println!("  Results: {} items", result1.len());
    println!();
    
    // Benchmark optimized approach
    let start = Instant::now();
    let result2 = optimized_approach(&data_map, &test_keys);
    let duration2 = start.elapsed();
    println!("Optimized approach:");
    println!("  Time: {:?}", duration2);
    println!("  Results: {} items", result2.len());
    println!();
    
    // Benchmark super optimized approach
    let start = Instant::now();
    let result3 = super_optimized_approach(&data_map, &test_keys);
    let duration3 = start.elapsed();
    println!("Super optimized approach:");
    println!("  Time: {:?}", duration3);
    println!("  Results: {} items", result3.len());
    println!();
    
    // Calculate improvements
    if duration1.as_nanos() > 0 {
        let improvement1 = (duration1.as_nanos() as f64 - duration2.as_nanos() as f64) / duration1.as_nanos() as f64 * 100.0;
        let improvement2 = (duration1.as_nanos() as f64 - duration3.as_nanos() as f64) / duration1.as_nanos() as f64 * 100.0;
        
        println!("Performance improvements:");
        println!("  Optimized vs Original: {:.1}% faster", improvement1);
        println!("  Super optimized vs Original: {:.1}% faster", improvement2);
    }
    
    // Verify results are equivalent
    assert_eq!(result1.len(), result2.len());
    assert_eq!(result1.len(), result3.len());
    println!();
    println!("✅ All approaches produce equivalent results");
    println!("✅ Optimization successful!");
}
