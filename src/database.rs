use crate::models::*;
use anyhow::Result;
use bson::{doc, Document};
use futures::stream::TryStreamExt;
use log::{error, info, warn};
use mongodb::{Client, Collection, Database};
use std::collections::HashMap;
use std::process::exit;

pub struct DatabaseService {
    db: Database,
}

impl DatabaseService {
    pub async fn new(connection_string: &str, database_name: &str) -> Result<Self> {
        info!(
            "Initializing DatabaseService with connection_string: {}, database: {}",
            connection_string, database_name
        );

        let client = match Client::with_uri_str(connection_string).await {
            Ok(client) => {
                info!("Successfully connected to MongoDB");
                client
            }
            Err(e) => {
                error!("Failed to connect to MongoDB: {}", e);
                return Err(e.into());
            }
        };

        let db = client.database(database_name);
        info!("Database service initialized successfully");
        Ok(Self { db })
    }

    /// Get applications by tenant
    pub async fn get_all_applications(&self, tenant_id: &str) -> Result<Vec<ApplicationPO>> {

        let collection: Collection<Document> = self.db.collection("apply_info");

        let mut tenant_ids: Vec<String> = vec!["default".to_string()];
        if tenant_id != "default" {
            tenant_ids.push(tenant_id.to_string());
        }

        let filter = doc! {
            "public_tenant_id": { "$in": tenant_ids }
        };

        let mut cursor = match collection.find(filter, None).await {
            Ok(cursor) => {
                cursor
            }
            Err(e) => {
                error!("Failed to execute applications query: {}", e);
                return Err(e.into());
            }
        };

        // Pre-allocate vector for better performance
        let mut applications = Vec::with_capacity(1024);
        let mut doc_count = 0;

        while let Some(doc) = cursor.try_next().await? {
            doc_count += 1;

            match bson::from_document::<ApplicationPO>(doc) {
                Ok(app) => {
                    applications.push(app);
                }
                Err(e) => {
                    warn!("Failed to parse document as ApplicationPO: {}", e);
                }
            }
        }

        info!(
            "Found {} applications (processed {} documents)",
            applications.len(),
            doc_count
        );
        Ok(self.convert_tree(&mut applications))
    }

    /// Convert flat application list to tree structure with optimized performance
    fn convert_tree(&self, applications: &mut Vec<ApplicationPO>) -> Vec<ApplicationPO> {
        // Build parent ID to children mapping with pre-allocated capacity
        let mut children_map: HashMap<String, Vec<ApplicationPO>> = HashMap::with_capacity(applications.len());

        // Group by parent ID in a single pass, avoiding unnecessary clones
        for app in applications.drain(..) {
            children_map
                .entry(app.parent_id.clone()) // Only clone the parent_id, not the whole app
                .or_insert_with(Vec::new)
                .push(app);
        }

        // Recursively build tree structure
        fn build_tree(
            nodes: Vec<ApplicationPO>,
            children_map: &HashMap<String, Vec<ApplicationPO>>,
        ) -> Vec<ApplicationPO> {
            nodes
                .into_iter()
                .map(|mut node| {
                    // Use get instead of cloned to avoid unnecessary cloning
                    if let Some(children) = children_map.get(&node.id) {
                        node.children = Some(build_tree(children.clone(), children_map));
                    }
                    node
                })
                .collect()
        }

        // 3. 从根节点开始构建树
        if let Some(root_nodes) = children_map.remove("root") {
            build_tree(root_nodes, &children_map)
        } else {
            Vec::new()
        }
    }

    /// Get models by environment
    pub async fn get_models_by_env(&self, env: &str) -> Result<HashMap<String, Vec<StructureDTO>>> {

        let collection: Collection<Document> = self.db.collection("apply_datatype_info");

        let filter = doc! { "env": env };

        let projection = doc! {
            "appId": 1,
            "name": 1,
            "env": 1,
        };
        let options = mongodb::options::FindOptions::builder()
            .projection(projection)
            .build();

        let mut cursor = match collection.find(filter, options).await {
            Ok(cursor) => {
                cursor
            }
            Err(e) => {
                error!("Failed to execute models query: {}", e);
                return Err(e.into());
            }
        };

        let mut property_map = self.list_all_properties().await?;

        let mut models_map = HashMap::with_capacity(1024);
        let mut doc_count = 0;

        while let Some(doc) = cursor.try_next().await? {
            doc_count += 1;

            match bson::from_document::<StructureDTO>(doc) {
                Ok(mut structure) => {
                    let app_id = structure.app_id.clone();
                    let structure_id = &structure.id;

                    if let Some(properties) = property_map.remove(structure_id) {
                        structure.properties = properties;
                    }

                    // Java: map.put(structure.getAppId(), list);
                    models_map
                        .entry(app_id)
                        .or_insert_with(Vec::new)
                        .push(structure);
                }
                Err(e) => {
                    warn!("Failed to parse document as StructureDTO: {}", e);
                }
            }
        }

        info!(
            "Found {} models across {} apps (processed {} documents)",
            models_map.values().map(|v| v.len()).sum::<usize>(),
            models_map.len(),
            doc_count
        );
        Ok(models_map)
    }

    pub async fn list_all_properties(&self) -> Result<HashMap<String, Vec<PropertyDTO>>> {

        let collection: Collection<Document> = self.db.collection("apply_datatype_param_info");

        let projection = doc! {
            "name": 1,
            "structureId": 1,
            "dataType": 1,
            "desc": 1,
        };
        let options = mongodb::options::FindOptions::builder()
            .projection(projection)
            .build();

        let mut cursor = match collection.find(None, options).await {
            Ok(cursor) => {
                info!("Successfully created cursor for properties query");
                cursor
            }
            Err(e) => {
                error!("Failed to execute properties query: {}", e);
                return Err(e.into());
            }
        };

        let mut property_map = HashMap::with_capacity(1024);
        let mut doc_count = 0;

        while let Some(doc) = cursor.try_next().await? {
            doc_count += 1;

            match bson::from_document::<PropertyDTO>(doc) {
                Ok(property) => {
                    let structure_id = property.structure_id.clone();
                    property_map
                        .entry(structure_id)
                        .or_insert_with(Vec::new)
                        .push(property);
                }
                Err(e) => {
                    // warn!("Failed to parse document as PropertyDTO: {} {:?}", e, doc);
                    warn!("Failed to parse document as PropertyDTO: {}", e);
                }
            }
        }

        info!(
            "Found {} property groups (processed {} documents)",
            property_map.len(),
            doc_count
        );
        Ok(property_map)
    }

    /// Get interfaces by environment
    pub async fn get_interfaces_by_env(
        &self,
        env: &str,
    ) -> Result<HashMap<String, Vec<InterfaceInfoVO>>> {

        let collection: Collection<Document> = self.db.collection("apply_interface_info");

        let filter = doc! { "env": env };

        let projection = doc! {
            "name": 1,
            "alias": 1,
            "appId": 1,
            "serviceName": 1,
            "uri": 1,
            "requestMethod": 1,
            "desc": 1,
        };
        let options = mongodb::options::FindOptions::builder()
            .projection(projection)
            .build();

        let mut cursor = match collection.find(filter, options).await {
            Ok(cursor) => {
                cursor
            }
            Err(e) => {
                error!("Failed to execute interfaces query: {}", e);
                return Err(e.into());
            }
        };

        let mut interfaces_map = HashMap::with_capacity(1024);
        let mut doc_count = 0;

        while let Some(doc) = cursor.try_next().await? {
            doc_count += 1;

            if let (
                Some(name),
                Some(id),
                Some(alias),
                Some(app_id),
                Some(uri),
                Some(request_method),
            ) = (
                doc.get_str("name").ok(),
                doc.get_str("_id").ok(),
                doc.get_str("alias").ok(),
                doc.get_str("appId").ok(),
                doc.get_str("uri").ok(),
                doc.get_str("requestMethod").ok(),
            ) {
                let interface_info = InterfaceInfoVO {
                    id: id.to_string(),
                    name: name.to_string(),
                    alias: alias.to_string(),
                    app_id: app_id.to_string(),
                    uri: uri.to_string(),
                    service_name: Some(doc.get_str("serviceName").unwrap_or("").to_string()),
                    request_method: request_method.to_string(),
                    desc: doc.get_str("desc").ok().map(|s| s.to_string()),
                    request: Vec::new(),
                    response: Vec::new(),
                };

                interfaces_map
                    .entry(app_id.to_string())
                    .or_insert_with(Vec::new)
                    .push(interface_info);
            } else {
                warn!(
                    "Failed to extract required fields from interface document id {}",
                    doc.get_str("serviceName").unwrap_or("").to_string()
                );
                // 退出程序
                exit(1)
            }
        }

        info!(
            "Found {} interfaces across {} apps (processed {} documents)",
            interfaces_map.values().map(|v| v.len()).sum::<usize>(),
            interfaces_map.len(),
            doc_count
        );
        Ok(interfaces_map)
    }

    /// Get result criteria by tenant
    pub async fn get_result_criteria(
        &self,
        tenant_id: &str,
    ) -> Result<HashMap<String, ResultCriteriaPO>> {

        let collection: Collection<Document> = self.db.collection("apply_api_result_criteria");

        let filter = doc! { "public_tenant_id": tenant_id };

        let mut cursor = match collection.find(filter, None).await {
            Ok(cursor) => {
                cursor
            }
            Err(e) => {
                error!("Failed to execute result criteria query: {}", e);
                return Err(e.into());
            }
        };

        let mut criteria_map = HashMap::with_capacity(1024);
        let mut doc_count = 0;

        while let Some(doc) = cursor.try_next().await? {
            doc_count += 1;

            if let (Some(interface_id), Some(id)) =
                (doc.get_str("interface_id").ok(), doc.get_str("_id").ok())
            {
                let mut criteria = ResultCriteriaPO {
                    id: id.to_string(),
                    interface_id: interface_id.to_string(),
                    criteria: Vec::new(),
                    public_tenant_id: doc.get_str("public_tenant_id").ok().map(|s| s.to_string()),
                };

                if let Ok(criterias_array) = doc.get_array("criterias") {
                    for criteria_value in criterias_array {
                        if let Some(criteria_doc) = criteria_value.as_document() {
                            let criteria_detail = CriteriaDetail {
                                key: criteria_doc.get_str("key").unwrap_or("").to_string(),
                                value: criteria_doc.get_str("value").unwrap_or("").to_string(),
                            };
                            criteria.criteria.push(criteria_detail);
                        }
                    }
                }

                criteria_map.insert(interface_id.to_string(), criteria);
            } else {
                warn!("Failed to extract required fields from result criteria document");
            }
        }

        info!(
            "Found {} result criteria (processed {} documents)",
            criteria_map.len(),
            doc_count
        );
        Ok(criteria_map)
    }

    /// Get all parameters
    pub async fn get_all_params(&self) -> Result<HashMap<String, Vec<ParamPO>>> {

        let collection: Collection<Document> = self.db.collection("apply_param_info");

        let filter = doc! {};
        let projection = doc! {
            "interfaceId": 1,
            "appId": 1,
            "position": 1,
            "order": 1,
            "name": 1,
            "dataType": 1,
            "desc": 1,
            "required": 1,
            "defaultValue": 1,
        };
        let options = mongodb::options::FindOptions::builder()
            .projection(projection)
            .build();

        let mut cursor = match collection.find(filter, options).await {
            Ok(cursor) => {
                cursor
            }
            Err(e) => {
                error!("Failed to execute params query: {}", e);
                return Err(e.into());
            }
        };

        let mut params_map = HashMap::with_capacity(1024);
        let mut doc_count = 0;

        while let Some(doc) = cursor.try_next().await? {
            doc_count += 1;

            match bson::from_document::<ParamPO>(doc.clone()) {
                Ok(param) => {
                    let interface_id = param.interface_id.clone();
                    params_map
                        .entry(interface_id)
                        .or_insert_with(Vec::new)
                        .push(param);
                }
                Err(e) => {
                    warn!("Failed to parse document as ParamPO: {}", e);
                }
            }
        }

        info!(
            "Found {} parameter groups across {} interfaces (processed {} documents)",
            params_map.values().map(|v| v.len()).sum::<usize>(),
            params_map.len(),
            doc_count
        );
        Ok(params_map)
    }


    pub async fn get_tenant(&self, tenant_id: &str) -> Result<Option<Tenant>> {

        let collection: Collection<Document> = self.db.collection("tenant");

        let filter = doc! { "_id": tenant_id };

        if let Some(doc) = collection.find_one(filter, None).await? {
            if let Ok(tenant) = bson::from_document::<Tenant>(doc) {
                return Ok(Some(tenant));
            }
        }
        Ok(None)
    }
}
