//! SDK3 Generator Library
//!
//! A Rust implementation of the Java SDK3 generator that creates Java SDK files
//! from MongoDB interface definitions. This library provides equivalent functionality
//! to the original Java implementation with improved performance and memory efficiency.
//!
//! # Features
//!
//! - MongoDB integration for retrieving interface definitions
//! - Hierarchical application processing
//! - Java code generation with template support
//! - Concurrent processing for improved performance
//! - Optimized memory usage and reduced cloning
//!
//! # Example
//!
//! ```rust,no_run
//! use sdk3_generator::{SDK3Generator, GenerateConfig};
//!
//! #[tokio::main]
//! async fn main() -> anyhow::Result<()> {
//!     let config = GenerateConfig {
//!         is_open: false,
//!         source_path: "source".to_string(),
//!         base_path: "/tmp/sdk".to_string(),
//!         env: "dev".to_string(),
//!     };
//!
//!     let generator = SDK3Generator::new(
//!         "mongodb://localhost:27017",
//!         "api_management",
//!         config
//!     ).await?;
//!
//!     // Generate SDK files
//!     // generator.generate_sdk3(&tenant, "dev").await?;
//!     Ok(())
//! }
//! ```

pub mod models;
pub mod database;
pub mod generator;
pub mod sdk3;
pub mod utils;

// Re-export commonly used types for convenience
pub use models::{
    ApplicationPO, InterfaceInfoVO, StructureDTO, ParamPO, PropertyDTO,
    ResultCriteriaPO, Tenant, ModelInfo, GenerateConfig, AppConfig
};
pub use database::DatabaseService;
pub use generator::DefaultGenerator;
pub use sdk3::SDK3Generator;
pub use utils::{PathUtils, FileUtils, is_empty};
