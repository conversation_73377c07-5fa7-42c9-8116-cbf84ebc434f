use clap::{Arg, Command};
use env_logger;
use log::{error, info};
use sdk3_generator::{AppConfig, SDK3Generator};
use std::fs;
use std::process;
use anyhow::Result;

/// Initialize logging with appropriate level
fn init_logging() {
    if std::env::var("RUST_LOG").is_err() {
        std::env::set_var("RUST_LOG", "info");
    }
    env_logger::init();
}

/// Load and parse configuration file
fn load_config(config_file: &str) -> Result<AppConfig> {
    info!("Loading configuration from: {}", config_file);
    let config_content = fs::read_to_string(config_file)
        .map_err(|e| anyhow::anyhow!("Failed to read config file '{}': {}", config_file, e))?;

    let app_config: AppConfig = toml::from_str(&config_content)
        .map_err(|e| anyhow::anyhow!("Failed to parse config file '{}': {}", config_file, e))?;

    Ok(app_config)
}

#[tokio::main]
async fn main() {
    init_logging();

    let matches = Command::new("SDK3 Generator")
        .version(env!("CARGO_PKG_VERSION"))
        .author("SDK3 Rust Implementation")
        .about("Generates Java SDK files from MongoDB interface definitions")
        .arg(
            Arg::new("config")
                .short('c')
                .long("config")
                .value_name("CONFIG_FILE")
                .help("Configuration file path (defaults to config.toml)")
                .required(false),
        )
        .get_matches();

    // Use default config file if not provided
    let config_file = matches
        .get_one::<String>("config")
        .map(|s| s.as_str())
        .unwrap_or("config.toml");

    // Load configuration with proper error handling
    let app_config = match load_config(config_file) {
        Ok(config) => config,
        Err(e) => {
            error!("{}", e);
            process::exit(1);
        }
    };
    println!("Starting SDK3 Generator");
    info!("Starting SDK3 Generator");
    info!("Connection String: {}", app_config.connection_string);
    info!("Database: {}", app_config.database);
    info!("Tenant ID: {}", app_config.tenant_id);
    info!("Environment: {}", app_config.generate.env);
    info!("Is Open: {}", app_config.generate.is_open);
    info!("Source Path: {}", app_config.generate.source_path);
    info!("Base Path: {}", app_config.generate.base_path);
    // tokio::select! {}

    match SDK3Generator::new(
        &app_config.connection_string,
        &app_config.database,
        app_config.generate.clone(),
    )
    .await
    {
        Ok(generator) => {
            info!("SDK3 Generator initialized successfully");

            if let Ok(tenant) = generator.tenant(&app_config.tenant_id).await {
                match generator
                    .generate_sdk3(&tenant, &app_config.generate.env)
                    .await
                {
                    Ok(()) => {
                        info!("SDK3 generation completed successfully!");
                        match generator.build(&tenant, &app_config.generate.env) {
                            Ok(()) => {
                                info!("SDK3 build completed successfully!");
                            }
                            Err(e) => {
                                error!("SDK3 build failed: {}", e);
                                process::exit(1);
                            }
                        }
                    }
                    Err(e) => {
                        error!("SDK3 generation failed: {}", e);
                        process::exit(1);
                    }
                }
            };
        }
        Err(e) => {
            error!("Failed to initialize SDK3 Generator: {}", e);
            process::exit(1);
        }
    }
}

#[cfg(test)]
mod tests {
    use sdk3_generator::*;

    #[tokio::test]
    async fn test_sdk3_generator_creation() {
        let config = GenerateConfig {
            is_open: false,
            source_path: "/tmp/test_source".to_string(),
            base_path: "/tmp/test_target".to_string(),
            env: "dev".to_string(),
        };

        // This would require a real MongoDB connection for full testing
        // For now, just test that the structure compiles
        assert_eq!(config.env, "dev");
    }

    #[test]
    fn test_path_utils() {
        let model_path =
            PathUtils::get_folder_model_path("test_app", false, "tenant1", "dev", "/tmp/test");
        assert!(model_path.contains("model"));

        let api_path =
            PathUtils::get_folder_api_path("test_app", false, "tenant1", "dev", "/tmp/test");
        assert!(api_path.contains("api"));

        let class_name = PathUtils::class_name("test_class_name");
        assert_eq!(class_name, "TestClassName");
    }

    #[test]
    fn test_file_utils() {
        use std::fs;
        use std::path::Path;

        let test_path = "/tmp/test_sdk3_file.txt";
        let test_content = "Test content";

        // Test file writing
        FileUtils::write_to_file(test_path, test_content).unwrap();
        assert!(Path::new(test_path).exists());

        // Test file content
        let content = fs::read_to_string(test_path).unwrap();
        assert_eq!(content, test_content);

        // Cleanup
        fs::remove_file(test_path).ok();
    }
}
