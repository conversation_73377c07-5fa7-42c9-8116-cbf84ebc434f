use serde::{Deserialize, Serialize};

/// Default value for String fields
const fn default_string() -> String {
    String::new()
}

/// Default ObjectId string for MongoDB documents
fn default_object_id() -> String {
    "000000000000000000000000".to_string()
}

/// Application data structure representing MongoDB application documents
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApplicationPO {
    /// Unique identifier for the application
    #[serde(rename = "_id", default = "default_object_id")]
    pub id: String,
    /// Application alias name
    #[serde(default)]
    pub alias: Option<String>,
    /// Application display name
    #[serde(default = "default_string")]
    pub name: String,
    /// Application description
    #[serde(default)]
    pub desc: Option<String>,
    /// Interface classification for the application
    #[serde(rename = "interfaceClassification", default)]
    pub interface_classification: Option<String>,
    /// Child applications in the hierarchy
    #[serde(default)]
    pub children: Option<Vec<ApplicationPO>>,
    /// Public tenant identifier
    #[serde(rename = "public_tenant_id", default)]
    pub public_tenant_id: Option<String>,
    /// Parent application identifier
    #[serde(rename = "parentId", default)]
    pub parent_id: String,
}

/// Interface information value object representing API interface definitions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InterfaceInfoVO {
    /// Unique identifier for the interface
    #[serde(rename = "_id", default = "default_object_id")]
    pub id: String,
    /// Application identifier this interface belongs to
    #[serde(rename = "appId", default = "default_string")]
    pub app_id: String,
    /// Interface name
    #[serde(default = "default_string")]
    pub name: String,
    /// Interface alias
    #[serde(default = "default_string")]
    pub alias: String,
    /// API endpoint URI
    #[serde(default = "default_string")]
    pub uri: String,
    /// Interface description
    #[serde(default)]
    pub desc: Option<String>,
    /// HTTP request method (GET, POST, etc.)
    #[serde(rename = "requestMethod", default = "default_string")]
    pub request_method: String,
    /// Service name for the interface
    #[serde(rename = "serviceName", default)]
    pub service_name: Option<String>,
    /// Request parameters
    #[serde(default)]
    pub request: Vec<ParamPO>,
    /// Response parameters
    #[serde(default)]
    pub response: Vec<ParamPO>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParamPO {
    #[serde(rename = "_id", default)]
    pub id: String,
    #[serde(rename = "interfaceId", default)]
    pub interface_id: String,
    #[serde(default)]
    pub name: String,
    #[serde(rename = "position", default)]
    pub position: String, // query, path, header, body
    #[serde(rename = "dataType", default)]
    pub data_type: DataType,
    #[serde(default)]
    pub required: Option<bool>,
    #[serde(default)]
    pub desc: Option<String>,
}
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct DataType {
    #[serde(default)]
    pub kind: String,
    #[serde(default)]
    pub name: String,
    #[serde(default)]
    pub type_values: Option<Vec<DataType>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StructureDTO {
    #[serde(rename = "_id", default)]
    pub id: String,
    #[serde(rename = "appId", default)]
    pub app_id: String,
    #[serde(default)]
    pub name: String,
    #[serde(default)]
    pub desc: Option<String>,
    #[serde(default)]
    pub properties: Vec<PropertyDTO>,
    #[serde(default)]
    pub env: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PropertyDTO {
    #[serde(rename = "_id", default)]
    pub id: String,
    #[serde(rename = "structureId", default)]
    pub structure_id: String,
    #[serde(default)]
    pub name: String,
    #[serde(rename = "dataType", default)]
    pub data_type: DataType,
    #[serde(default)]
    pub required: Option<bool>,
    #[serde(default)]
    pub desc: Option<String>,
    #[serde(default)]
    pub order: Option<i32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CriteriaDetail {
    pub key: String,
    pub value: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResultCriteriaPO {
    #[serde(rename = "_id", default)]
    pub id: String,
    #[serde(rename = "interface_id", default)]
    pub interface_id: String,
    #[serde(rename = "criterias", default)]
    pub criteria: Vec<CriteriaDetail>,
    #[serde(rename = "public_tenant_id", default)]
    pub public_tenant_id: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Tenant {
    #[serde(rename = "_id")]
    pub id: String,
    #[serde(rename = "tenantEnglishName")]
    pub tenant_english_name: String,
    pub name: String,
}

#[derive(Debug, Clone)]
pub struct ModelInfo {
    pub folder_model_path: String,
    pub import_model: String,
    pub folder_api_path: String,
    pub import_api: String,
    pub fication: String,
    pub models: Vec<StructureDTO>,
    pub app_api: Vec<InterfaceInfoVO>,
    pub app: ApplicationPO,
    pub english_name: String,
    pub tenant_id: String,
    pub env: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GenerateConfig {
    pub is_open: bool,
    pub source_path: String,
    pub base_path: String,
    pub env: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
    pub connection_string: String,
    pub database: String,
    pub tenant_id: String,
    pub generate: GenerateConfig,
}
