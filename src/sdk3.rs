use crate::database::DatabaseService;
use crate::generator::DefaultGenerator;
use crate::models::*;
use crate::utils::*;
use anyhow::Result;
use futures::future::join_all;
use log::{info, warn};
use std::collections::HashMap;
use std::time::Instant;

pub struct SDK3Generator {
    db_service: DatabaseService,
    config: GenerateConfig,
}

impl SDK3Generator {
    pub async fn new(
        connection_string: &str,
        database_name: &str,
        config: GenerateConfig,
    ) -> Result<Self> {
        let db_service = DatabaseService::new(connection_string, database_name).await?;

        Ok(Self { db_service, config })
    }

    pub async fn tenant(&self, tenant_id: &str) -> Result<Tenant> {
        let tenant = self
            .db_service
            .get_tenant(tenant_id)
            .await?
            .ok_or_else(|| anyhow::anyhow!("Tenant not found: {}", tenant_id))?;
        Ok(tenant)
    }

    /// Main SDK3 method - equivalent to the Java sdk3() method
    /// Optimized to reduce unnecessary cloning and improve performance
    pub async fn generate_sdk3(&self, tenant: &Tenant, env: &str) -> Result<()> {
        let start_time = Instant::now();
        let tenant_id = &tenant.id; // Avoid unnecessary clone
        info!(
            "Starting SDK3 generation for tenant: {}, env: {}",
            tenant_id, env
        );

        // mkdir - equivalent to Java mkdir() method
        self.mkdir(tenant, self.config.is_open, env)?;

        // Get all applications for the tenant
        let all_apps = self.db_service.get_all_applications(tenant_id).await?;
        info!("Found {} applications", all_apps.len());

        // Pre-load all data maps for better performance (like Java sdk3 method)
        let models_map = self.db_service.get_models_by_env(env).await?;
        info!("Loaded {} model groups", models_map.len());
        let interfaces_map = self.db_service.get_interfaces_by_env(env).await?;
        info!("Loaded {} interface groups", interfaces_map.len());
        let criteria_map = self.db_service.get_result_criteria(tenant_id).await?;
        info!("Loaded {} result criteria", criteria_map.len());
        let params_map = self.db_service.get_all_params().await?;
        info!("Loaded {} params", params_map.len());

        // Process applications recursively with concurrent execution
        // Pre-allocate vector with estimated capacity
        let mut futures = Vec::with_capacity(all_apps.len());

        for app in all_apps {
            if is_empty(&app.alias) {
                continue;
            }

            // Extract fication before moving app
            let fication = app.interface_classification.clone().unwrap_or_default();
            let future = self.recursion_generate3(
                &models_map,
                &interfaces_map,
                &criteria_map,
                &params_map,
                tenant, // Pass reference instead of clone
                env,    // Pass reference instead of clone
                app,    // Move app here
                String::new(),
                fication, // Use the extracted value
            );
            futures.push(future);
        }

        // Wait for all tasks to complete
        join_all(futures).await;

        // Generate static utilities
        self.generate_static_path(tenant, env)?;

        let duration = start_time.elapsed();
        warn!("SDK3 generation completed in {:?}", duration);
        Ok(())
    }

    /// Recursive generation method - equivalent to recursionGenerate3
    /// Recursively generate SDK files for application hierarchy
    /// Optimized to reduce cloning and improve performance
    async fn recursion_generate3(
        &self,
        models_map: &HashMap<String, Vec<StructureDTO>>,
        interfaces_map: &HashMap<String, Vec<InterfaceInfoVO>>,
        criteria_map: &HashMap<String, ResultCriteriaPO>,
        params_map: &HashMap<String, Vec<ParamPO>>,
        tenant: &Tenant,
        env: &str,
        app: ApplicationPO,
        alias_name: String,
        fication: String,
    ) -> Result<()> {
        // Build current folder path more efficiently
        let current_folder = if alias_name.is_empty() {
            app.alias.as_deref().unwrap_or_default().to_string()
        } else {
            format!(
                "{}{}{}",
                alias_name,
                PathUtils::IMPORT,
                app.alias.as_deref().unwrap_or_default()
            )
        };

        // Generate for current application
        self.generate3(
            models_map,
            interfaces_map,
            criteria_map,
            params_map,
            tenant,
            env,
            &app,
            &current_folder,
            &fication,
        )
        .await?;

        // Process children recursively
        if let Some(children) = &app.children {
            // Pre-allocate vector with known capacity
            let mut child_futures = Vec::with_capacity(children.len());

            for child in children {
                if is_empty(&child.alias) {
                    continue;
                }

                let future = self.recursion_generate3(
                    models_map,
                    interfaces_map,
                    criteria_map,
                    params_map,
                    tenant,
                    env,
                    child.clone(),          // Still need to clone child for ownership
                    current_folder.clone(), // Still need to clone for ownership
                    fication.clone(),       // Still need to clone for ownership
                );
                child_futures.push(future);
            }
            join_all(child_futures).await;
        }

        Ok(())
    }

    /// Generate method - equivalent to generate3
    /// Optimized to reduce unnecessary cloning from HashMap lookups
    async fn generate3(
        &self,
        models_map: &HashMap<String, Vec<StructureDTO>>,
        interfaces_map: &HashMap<String, Vec<InterfaceInfoVO>>,
        criteria_map: &HashMap<String, ResultCriteriaPO>,
        params_map: &HashMap<String, Vec<ParamPO>>,
        tenant: &Tenant,
        env: &str,
        app: &ApplicationPO,
        current_folder: &str,
        fication: &str,
    ) -> Result<()> {
        // Get models for this application - only get reference first
        let models_ref = models_map.get(&app.id);

        // Get APIs for this application with enhanced data - optimized to avoid multiple clones
        let app_api = self.query_interface_optimized(&app.id, interfaces_map, criteria_map, params_map);

        // Create ModelInfo with optimized path building and minimal cloning
        let model_info = Self::build_model_info(
            current_folder,
            &self.config,
            tenant,
            env,
            fication,
            models_ref.cloned().unwrap_or_default(),
            app_api,
            app.clone(),
        );

        // Generate Java files
        self.generate_java_files(model_info).await?;

        Ok(())
    }



    /// Optimized query interface method - reduces unnecessary cloning
    fn query_interface_optimized(
        &self,
        app_id: &str,
        interfaces_map: &HashMap<String, Vec<InterfaceInfoVO>>,
        criteria_map: &HashMap<String, ResultCriteriaPO>,
        params_map: &HashMap<String, Vec<ParamPO>>,
    ) -> Vec<InterfaceInfoVO> {
        // Get interfaces reference first, avoid immediate clone
        let interfaces_ref = match interfaces_map.get(app_id) {
            Some(interfaces) => interfaces,
            None => return Vec::new(), // Early return if no interfaces
        };

        // Pre-allocate result vector with known capacity
        let mut result = Vec::with_capacity(interfaces_ref.len());

        // Process each interface, only cloning when necessary
        for interface in interfaces_ref {
            let mut enhanced_interface = interface.clone(); // Clone only once per interface

            // Add request/response parameters if they exist
            if let Some(params) = params_map.get(&interface.id) {
                // Use iterators with collect for better performance
                enhanced_interface.request = params
                    .iter()
                    .filter(|p| matches!(p.position.as_str(),
                        "requestPath" | "requestQuery" | "requestHeader" | "requestBody"))
                    .cloned()
                    .collect();

                enhanced_interface.response = params
                    .iter()
                    .filter(|p| matches!(p.position.as_str(),
                        "responseBody" | "responseHeader"))
                    .cloned()
                    .collect();
            }

            // Add criteria if available (placeholder for future logic)
            if let Some(_criteria) = criteria_map.get(&interface.id) {
                // Apply criteria logic if needed
            }

            result.push(enhanced_interface);
        }

        result
    }

    /// Optimized ModelInfo builder to reduce path computation overhead
    fn build_model_info(
        current_folder: &str,
        config: &GenerateConfig,
        tenant: &Tenant,
        env: &str,
        fication: &str,
        models: Vec<StructureDTO>,
        app_api: Vec<InterfaceInfoVO>,
        app: ApplicationPO,
    ) -> ModelInfo {
        ModelInfo {
            folder_model_path: PathUtils::get_folder_model_path(
                current_folder,
                config.is_open,
                &tenant.tenant_english_name,
                env,
                &config.base_path,
            ),
            import_model: PathUtils::get_import_model(current_folder, config.is_open),
            folder_api_path: PathUtils::get_folder_api_path(
                current_folder,
                config.is_open,
                &tenant.tenant_english_name,
                env,
                &config.base_path,
            ),
            import_api: PathUtils::get_import_api(current_folder, config.is_open),
            fication: fication.to_string(),
            models,
            app_api,
            app,
            english_name: tenant.tenant_english_name.clone(),
            tenant_id: tenant.id.clone(),
            env: env.to_string(),
        }
    }

    /// Generate Java files for models and APIs
    async fn generate_java_files(&self, model_info: ModelInfo) -> Result<()> {
        // Use DefaultGenerator following Java logic exactly
        let generator = DefaultGenerator::new(model_info);
        generator.generate()?;
        Ok(())
    }

    /// mkdir method - equivalent to Java mkdir() method
    fn mkdir(&self, tenant: &Tenant, is_open: bool, env: &str) -> Result<()> {
        let target_source = self.target_folder(tenant, is_open, env);
        let source_path = &self.config.source_path;

        let pom = format!("{}{}", target_source, DefaultGenerator::POM);
        let pom_file = std::path::Path::new(&pom);

        if !pom_file.exists() {
            // 如果文件不存在
            info!("{} ====>>> {}", source_path, target_source);
            self.directory_copy(source_path, &target_source)?;
            self.set_pom(&pom, &tenant.tenant_english_name)?;
        } else {
            let remove_target_source = PathUtils::remove_target_source(
                is_open,
                &tenant.tenant_english_name,
                env,
                &self.config.base_path,
            );
            FileUtils::clean_directory(&remove_target_source)?;
        }

        Ok(())
    }

    /// target_folder method - equivalent to Java targetFolder() method
    fn target_folder(&self, tenant: &Tenant, is_open: bool, env: &str) -> String {
        format!(
            "{}{}{}",
            PathUtils::target_source(is_open, env, &self.config.base_path),
            tenant.tenant_english_name,
            PathUtils::FOLDER
        )
    }

    /// directory_copy method - equivalent to Java directoryCopy() method
    fn directory_copy(&self, source_dir_path: &str, target_dir_path: &str) -> Result<()> {
        use std::process::Command;

        // Create target directory if it doesn't exist
        FileUtils::ensure_directory_exists(target_dir_path)?;

        // Use cp command to copy directory (equivalent to FileUtils.copyDirectory in Java)
        let output = Command::new("cp")
            .arg("-r")
            .arg(source_dir_path)
            .arg(target_dir_path)
            .output()?;

        if !output.status.success() {
            let error = String::from_utf8_lossy(&output.stderr);
            return Err(anyhow::anyhow!("Directory copy failed: {}", error));
        }

        info!(
            "Directory copied successfully from {} to {}",
            source_dir_path, target_dir_path
        );
        Ok(())
    }

    /// set_pom method - equivalent to Java setPom() method
    fn set_pom(&self, source_pom: &str, english_name: &str) -> Result<()> {
        // Read template content (simplified - in real implementation you'd read from template)
        let template_content = self.read_template(source_pom)?;
        let contents = template_content.replace("${version}", english_name);
        FileUtils::write_to_file(source_pom, &contents)?;
        Ok(())
    }

    /// read_template method - equivalent to Java readTemplate() method
    fn read_template(&self, template_path: &str) -> Result<String> {
        use std::fs;

        // Read template content from file
        match fs::read_to_string(template_path) {
            Ok(content) => Ok(content),
            Err(_) => {
                // Fallback to default template if file doesn't exist
                Ok(r#"<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.client</groupId>
    <artifactId>java-client-${version}</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>
</project>"#
                    .to_string())
            }
        }
    }

    fn generate_static_path(&self, tenant: &Tenant, _env: &str) -> Result<()> {
        // Generate static utility files (equivalent to staticPath method)
        info!(
            "Generating static utilities for tenant: {}",
            tenant.tenant_english_name
        );
        // Implementation would go here for static file generation
        Ok(())
    }

    pub fn build(&self, tenant: &Tenant, env: &String) -> Result<()> {
        let target_source = self.target_folder(tenant, false, env);
        info!("Starting build process in directory: {}", target_source);

        // 检查 build.sh 文件是否存在
        let build_script = format!("{}/build.sh", target_source);
        if !std::path::Path::new(&build_script).exists() {
            warn!("build.sh not found in {}, skipping build", target_source);
            return Ok(());
        }

        // 执行 build.sh 并实时打印脚本输出
        let mut child = std::process::Command::new("sh")
            .arg("build.sh")
            .current_dir(&target_source)
            .stdout(std::process::Stdio::piped())
            .stderr(std::process::Stdio::piped())
            .spawn()
            .map_err(|e| anyhow::anyhow!("Failed to start build.sh: {}", e))?;

        // 获取子进程的 stdout 和 stderr
        let stdout = child.stdout.take().unwrap();
        let stderr = child.stderr.take().unwrap();

        // 使用 BufReader 实时读取输出
        use std::io::{BufRead, BufReader};
        use std::thread;

        // 创建线程来处理 stdout
        let stdout_handle = thread::spawn(move || {
            let reader = BufReader::new(stdout);
            for line in reader.lines() {
                match line {
                    Ok(line) => println!("[BUILD] {}", line),
                    Err(e) => eprintln!("[BUILD ERROR] Failed to read stdout: {}", e),
                }
            }
        });

        // 创建线程来处理 stderr
        let stderr_handle = thread::spawn(move || {
            let reader = BufReader::new(stderr);
            for line in reader.lines() {
                match line {
                    Ok(line) => eprintln!("[BUILD STDERR] {}", line),
                    Err(e) => eprintln!("[BUILD ERROR] Failed to read stderr: {}", e),
                }
            }
        });

        // 等待子进程完成
        let status = child
            .wait()
            .map_err(|e| anyhow::anyhow!("Failed to wait for build.sh: {}", e))?;

        // 等待输出线程完成
        stdout_handle.join().unwrap();
        stderr_handle.join().unwrap();

        if status.success() {
            info!("Build completed successfully");
        } else {
            return Err(anyhow::anyhow!(
                "Build failed with exit code: {:?}",
                status.code()
            ));
        }

        Ok(())
    }
}
