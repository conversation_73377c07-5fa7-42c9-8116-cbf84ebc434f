use std::fs;
use std::path::Path;
use anyhow::Result;

/// Utility functions for path manipulation and file operations
pub struct PathUtils;

impl PathUtils {
    pub const FOLDER: &'static str = "/";
    pub const SRC_PATH: &'static str = "/src/main/java/com/client/sdk/";
    pub const IMPORT_SRC: &'static str = "com.client.sdk";
    pub const IMPORT: &'static str = ".";
    pub const IMPORT_MODEL: &'static str = "model";
    pub const IMPORT_API: &'static str = "api";

    /// Get the folder path for model files
    pub fn get_folder_model_path(app: &str, is_open: bool, tenant_english_name: &str, env: &str, base_path: &str) -> String {
        format!("{}{}{}",
            Self::get_folder_path(app, is_open, tenant_english_name, env, base_path),
            Self::IMPORT_MODEL,
            Self::FOLDER
        )
    }

    /// Get the folder path for API files
    pub fn get_folder_api_path(app: &str, is_open: bool, tenant_english_name: &str, env: &str, base_path: &str) -> String {
        format!("{}{}{}",
            Self::get_folder_path(app, is_open, tenant_english_name, env, base_path),
            Self::IMPORT_API,
            Self::FOLDER
        )
    }

    /// Get the import path for model classes
    pub fn get_import_model(app: &str, _is_open: bool) -> String {
        format!("{}{}", Self::get_import_path(app), Self::IMPORT_MODEL)
    }

    /// Get the import path for API classes
    pub fn get_import_api(app: &str, _is_open: bool) -> String {
        format!("{}{}", Self::get_import_path(app), Self::IMPORT_API)
    }

    /// Build the base import path for an application
    fn get_import_path(app: &str) -> String {
        format!("{}{}{}{}", Self::IMPORT_SRC, Self::IMPORT, app, Self::IMPORT)
    }

    /// Get the target source directory based on open/closed tenant type
    pub fn target_source(is_open: bool, env: &str, base_path: &str) -> String {
        let target_type = if is_open { "open" } else { "all" };
        format!("{}/{}/{}/", base_path, target_type, env)
    }

    /// Remove target source method - equivalent to Java removeTargetSource()
    pub fn remove_target_source(is_open: bool, tenant_english_name: &str, env: &str, base_path: &str) -> String {
        format!("{}{}{}{}",
            Self::target_source(is_open, env, base_path),
            Self::FOLDER,
            tenant_english_name,
            Self::SRC_PATH
        )
    }

    /// Build the complete folder path for an application
    fn get_folder_path(app: &str, is_open: bool, tenant_english_name: &str, env: &str, base_path: &str) -> String {
        let tenant_part = if tenant_english_name.is_empty() {
            String::new()
        } else {
            tenant_english_name.to_string()
        };

        format!("{}{}{}{}{}",
            Self::target_source(is_open, env, base_path),
            tenant_part,
            Self::SRC_PATH,
            app.replace(Self::IMPORT, Self::FOLDER),
            Self::FOLDER
        )
    }

    /// Generate Java file name with .java extension
    pub fn java_name(folder_path: &str, class_name: &str) -> String {
        format!("{}{}.java", folder_path, class_name)
    }

    /// Convert a string to PascalCase for Java class names
    /// Optimized version with better memory allocation
    pub fn class_name(name: &str) -> String {
        if name.is_empty() {
            return String::new();
        }

        // Pre-allocate with estimated capacity to reduce reallocations
        let mut result = String::with_capacity(name.len());
        let mut capitalize_next = true;

        for ch in name.chars() {
            if ch.is_alphanumeric() {
                if capitalize_next {
                    // More efficient uppercase conversion
                    result.extend(ch.to_uppercase());
                    capitalize_next = false;
                } else {
                    result.push(ch);
                }
            } else {
                capitalize_next = true;
            }
        }

        result
    }
}

/// File system utility functions
pub struct FileUtils;

impl FileUtils {
    /// Ensure the parent directory exists for a given file path
    pub fn ensure_directory_exists(path: &str) -> Result<()> {
        if let Some(parent) = Path::new(path).parent() {
            fs::create_dir_all(parent)?;
        }
        Ok(())
    }

    /// Write content to a file, creating directories as needed
    pub fn write_to_file(path: &str, content: &str) -> Result<()> {
        Self::ensure_directory_exists(path)?;
        fs::write(path, content)?;
        Ok(())
    }

    /// Clean and recreate a directory
    pub fn clean_directory(path: &str) -> Result<()> {
        let path_obj = Path::new(path);
        if path_obj.exists() {
            fs::remove_dir_all(path_obj)?;
        }
        fs::create_dir_all(path_obj)?;
        Ok(())
    }
}

/// Check if an optional string is empty or contains only whitespace
pub fn is_empty(s: &Option<String>) -> bool {
    match s {
        Some(string) => string.trim().is_empty(),
        None => true,
    }
}
