#!/bin/bash

# SDK3 Generator Test Example Script

set -e

echo "SDK3 Generator Test Example"
echo "=========================="

# Configuration
MONGODB_URI="mongodb://localhost:27017"
DATABASE="api_management"
TENANT_ID="test_tenant"
ENV="dev"
TARGET_PATH="/tmp/sdk3_test_output"

echo "Configuration:"
echo "  MongoDB URI: $MONGODB_URI"
echo "  Database: $DATABASE"
echo "  Tenant ID: $TENANT_ID"
echo "  Environment: $ENV"
echo "  Target Path: $TARGET_PATH"
echo ""

# Check if MongoDB is running (optional)
echo "Checking MongoDB connection..."
if command -v mongosh &> /dev/null; then
    if mongosh --eval "db.runCommand('ping')" "$MONGODB_URI" &> /dev/null; then
        echo "✅ MongoDB is accessible"
    else
        echo "⚠️  Warning: Cannot connect to MongoDB at $MONGODB_URI"
        echo "   Make sure MongoDB is running and accessible"
    fi
else
    echo "ℹ️  mongosh not found, skipping MongoDB connectivity check"
fi

echo ""

# Build the project
echo "Building SDK3 Generator..."
if [ ! -f "target/release/sdk3" ]; then
    echo "Building project..."
    cargo build --release
fi

# Create target directory
echo "Creating target directory: $TARGET_PATH"
mkdir -p "$TARGET_PATH"

# Run the generator
echo ""
echo "Running SDK3 Generator..."
echo "Command: ./target/release/sdk3 \\"
echo "  --connection-string '$MONGODB_URI' \\"
echo "  --database '$DATABASE' \\"
echo "  --tenant-id '$TENANT_ID' \\"
echo "  --env '$ENV' \\"
echo "  --target-path '$TARGET_PATH'"
echo ""

# Set log level for verbose output
export RUST_LOG=info

# Execute the generator
if ./target/release/sdk3 \
  --connection-string "$MONGODB_URI" \
  --database "$DATABASE" \
  --tenant-id "$TENANT_ID" \
  --env "$ENV" \
  --target-path "$TARGET_PATH"; then
    
    echo ""
    echo "✅ SDK3 Generation completed successfully!"
    echo ""
    echo "Generated files location: $TARGET_PATH"
    
    # Show generated structure
    if [ -d "$TARGET_PATH" ]; then
        echo ""
        echo "Generated directory structure:"
        find "$TARGET_PATH" -type f -name "*.java" | head -20 | while read file; do
            echo "  📄 $file"
        done
        
        total_files=$(find "$TARGET_PATH" -type f -name "*.java" | wc -l)
        echo "  ... and $total_files total Java files"
    fi
    
else
    echo ""
    echo "❌ SDK3 Generation failed!"
    echo "Check the logs above for error details"
    exit 1
fi

echo ""
echo "Test completed!"
